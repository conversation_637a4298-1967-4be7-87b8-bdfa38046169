/**
 * 优化后的设备统计数据库模型
 * 针对存储空间限制进行优化，支持100万+设备记录
 */

import { pgTable, serial, varchar, integer, char, timestamp, text, index, uniqueIndex } from 'drizzle-orm/pg-core';
import { relations } from 'drizzle-orm';

// 设备统计主表
export const deviceStats = pgTable('device_stats', {
  id: serial('id').primaryKey(),
  deviceFingerprint: varchar('device_fingerprint', { length: 64 }).notNull().unique(),
  installRank: integer('install_rank').notNull(),
  countryCode: char('country_code', { length: 2 }),
  regionCode: varchar('region_code', { length: 10 }),
  osVersion: varchar('os_version', { length: 20 }),
  arch: varchar('arch', { length: 10 }),
  runCount: integer('run_count').default(1).notNull(),
  firstSeen: timestamp('first_seen').defaultNow().notNull(),
  lastSeen: timestamp('last_seen').defaultNow().notNull(),
  createdAt: timestamp('created_at').defaultNow().notNull(),
  updatedAt: timestamp('updated_at').defaultNow().notNull(),
}, (table) => ({
  deviceFingerprintIdx: uniqueIndex('idx_device_fingerprint').on(table.deviceFingerprint),
  installRankIdx: index('idx_install_rank').on(table.installRank),
  countryRegionIdx: index('idx_country_region').on(table.countryCode, table.regionCode),
  lastSeenIdx: index('idx_last_seen').on(table.lastSeen),
  runCountIdx: index('idx_run_count').on(table.runCount),
}));

// 地理位置代码表
export const geoCodes = pgTable('geo_codes', {
  id: serial('id').primaryKey(),
  countryCode: char('country_code', { length: 2 }).notNull(),
  countryName: varchar('country_name', { length: 50 }).notNull(),
  regionCode: varchar('region_code', { length: 10 }),
  regionName: varchar('region_name', { length: 50 }),
}, (table) => ({
  countryRegionUnique: uniqueIndex('geo_codes_country_region_unique').on(table.countryCode, table.regionCode),
}));

// 关系定义
export const deviceStatsRelations = relations(deviceStats, ({ one }) => ({
  geoInfo: one(geoCodes, {
    fields: [deviceStats.countryCode, deviceStats.regionCode],
    references: [geoCodes.countryCode, geoCodes.regionCode],
  }),
}));

export const geoCodesRelations = relations(geoCodes, ({ many }) => ({
  devices: many(deviceStats),
}));

// TypeScript 类型定义
export type DeviceStats = typeof deviceStats.$inferSelect;
export type NewDeviceStats = typeof deviceStats.$inferInsert;
export type GeoCodes = typeof geoCodes.$inferSelect;
export type NewGeoCodes = typeof geoCodes.$inferInsert;

// 扩展类型定义
export interface DeviceStatsWithGeo extends DeviceStats {
  geoInfo?: GeoCodes;
}

export interface DeviceStatsRequest {
  deviceFingerprint: string;
  ipAddress?: string;
  osVersion?: string;
  arch?: string;
  userAgent?: string;
}

export interface DeviceStatsResponse {
  success: boolean;
  installRank: number;
  runCount: number;
  isNewDevice: boolean;
  message?: string;
}

export interface StatsOverview {
  totalDevices: number;
  totalRuns: number;
  averageRunsPerDevice: number;
  countriesCount: number;
  latestInstallRank: number;
  activeLast7Days: number;
  activeLast30Days: number;
  topCountries: Array<{
    countryName: string;
    countryCode: string;
    deviceCount: number;
    totalRuns: number;
  }>;
  recentActivity: Array<{
    date: string;
    activeDevices: number;
    newDevices: number;
  }>;
}

export interface CountryStats {
  countryName: string;
  countryCode: string;
  deviceCount: number;
  totalRuns: number;
  averageRuns: number;
  lastActivity: Date;
}

export interface DeviceActivityTrend {
  date: string;
  activeDevices: number;
  newDevices: number;
  totalRuns: number;
}

// 地理位置解析结果
export interface GeoLocation {
  countryCode: string;
  countryName: string;
  regionCode?: string;
  regionName?: string;
}

// 批量操作类型
export interface BatchRunCountUpdate {
  deviceFingerprint: string;
  increment: number;
}

// 设备查询过滤器
export interface DeviceStatsFilter {
  countryCode?: string;
  regionCode?: string;
  osVersion?: string;
  arch?: string;
  minRunCount?: number;
  maxRunCount?: number;
  dateFrom?: Date;
  dateTo?: Date;
  limit?: number;
  offset?: number;
}

// 统计查询结果
export interface DeviceStatsQuery {
  devices: DeviceStatsWithGeo[];
  total: number;
  hasMore: boolean;
}

// 系统架构枚举
export const SUPPORTED_ARCHITECTURES = [
  'x64',
  'x86',
  'arm64',
  'arm',
  'unknown'
] as const;

export type SupportedArchitecture = typeof SUPPORTED_ARCHITECTURES[number];

// 操作系统版本格式
export interface OSVersionInfo {
  name: string;      // Windows, macOS, Linux
  version: string;   // 10, 11, 12.0, etc.
  build?: string;    // 构建号
}

// 设备指纹生成配置
export interface DeviceFingerprintConfig {
  includeHardware: boolean;
  includeOS: boolean;
  includeNetwork: boolean;
  hashLength: number;
}

// 数据库性能监控
export interface DatabasePerformanceInfo {
  tableName: string;
  sizeFormatted: string;
  sizeBytes: number;
  recordCount: number;
  indexCount: number;
}

// 数据清理配置
export interface DataCleanupConfig {
  inactiveDays: number;
  minRunCount: number;
  dryRun: boolean;
}

export interface DataCleanupResult {
  deletedCount: number;
  freedSpaceBytes: number;
  remainingDevices: number;
}

// 常用查询常量
export const QUERY_LIMITS = {
  DEFAULT_PAGE_SIZE: 50,
  MAX_PAGE_SIZE: 1000,
  STATS_TOP_COUNTRIES: 10,
  RECENT_ACTIVITY_DAYS: 30,
} as const;

// 地理位置常量
export const GEO_CONSTANTS = {
  UNKNOWN_COUNTRY: 'XX',
  UNKNOWN_REGION: 'XX',
  DEFAULT_COUNTRY_NAME: '未知',
  DEFAULT_REGION_NAME: '未知',
} as const;

// 设备指纹常量
export const FINGERPRINT_CONSTANTS = {
  MIN_LENGTH: 16,
  MAX_LENGTH: 64,
  DEFAULT_LENGTH: 32,
} as const;

// 验证函数
export const validateDeviceFingerprint = (fingerprint: string): boolean => {
  return fingerprint.length >= FINGERPRINT_CONSTANTS.MIN_LENGTH && 
         fingerprint.length <= FINGERPRINT_CONSTANTS.MAX_LENGTH &&
         /^[a-fA-F0-9]+$/.test(fingerprint);
};

export const validateCountryCode = (code: string): boolean => {
  return /^[A-Z]{2}$/.test(code);
};

export const validateRegionCode = (code: string): boolean => {
  return code.length <= 10 && /^[A-Z0-9]+$/.test(code);
};

// 默认值
export const DEFAULT_DEVICE_STATS: Partial<NewDeviceStats> = {
  runCount: 1,
  arch: 'unknown',
  osVersion: 'unknown',
};

export const DEFAULT_GEO_CODES: Partial<NewGeoCodes> = {
  countryCode: GEO_CONSTANTS.UNKNOWN_COUNTRY,
  countryName: GEO_CONSTANTS.DEFAULT_COUNTRY_NAME,
  regionCode: GEO_CONSTANTS.UNKNOWN_REGION,
  regionName: GEO_CONSTANTS.DEFAULT_REGION_NAME,
};

// 导出表定义供其他模块使用
export { deviceStats as deviceStatsTable, geoCodes as geoCodesTable };
