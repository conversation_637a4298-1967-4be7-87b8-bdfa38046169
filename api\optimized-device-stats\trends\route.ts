/**
 * 活动趋势API路由
 * 提供设备活动趋势数据
 */

import { NextRequest, NextResponse } from 'next/server';
import { OptimizedDeviceStatsService } from '../../../services/optimized-device-stats-service';

// 初始化服务
const deviceStatsService = new OptimizedDeviceStatsService(process.env.DATABASE_URL!);

/**
 * GET /api/optimized-device-stats/trends
 * 获取活动趋势
 */
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const days = searchParams.get('days') ? parseInt(searchParams.get('days')!) : 30;

    const trends = await deviceStatsService.getRecentActivityTrend(days);

    return NextResponse.json({
      success: true,
      data: trends,
    });

  } catch (error) {
    console.error('获取活动趋势失败:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: '服务器内部错误',
        details: error.message 
      },
      { status: 500 }
    );
  }
}
