# 隐私政策默认同意功能使用说明

## 📋 功能概述

根据您的需求，我们已经实现了隐私政策和使用条款中匿名数据上传默认同意的功能。如果用户不同意必要条款，应用将无法继续使用并提示退出。

## 🔧 实现的功能

### 1. 匿名数据收集默认同意
- **默认状态**: 匿名数据收集选项默认为已勾选状态
- **必需性**: 将匿名数据收集标记为软件正常运行的必需项
- **用户界面**: 明确标注为"必需"，并说明不同意将无法使用软件

### 2. 强制同意机制
- **全部必需**: 用户协议、隐私政策、匿名数据收集三项都是必需的
- **无法跳过**: 不同意任何一项都无法继续使用软件
- **退出提示**: 不同意时显示退出确认对话框

### 3. 退出应用功能
- **确认对话框**: 显示详细的退出原因和说明
- **安全退出**: 使用 Tauri API 安全退出应用
- **兜底机制**: 如果 API 失败，使用浏览器方法退出

## 📁 修改的文件

### 1. 核心组件
- `src/components/Welcome/pages/AgreementPage.tsx` - 用户协议页面
- `src/components/StartupFlow/WelcomeScreen.tsx` - 欢迎屏幕
- `src/components/StartupFlow/InitialSetupWizard.tsx` - 初始设置向导
- `src/components/Settings/OtherSettingsPanel.tsx` - 设置面板

### 2. 新增组件
- `src/components/Common/ExitConfirmDialog.tsx` - 退出确认对话框

### 3. 状态管理
- `src/stores/welcomeStore.ts` - 欢迎页面状态管理

### 4. 测试文件
- `test-privacy-policy.js` - 功能测试脚本

## 🎯 关键变更

### 1. 默认值设置
```typescript
// src/stores/welcomeStore.ts
analyticsConsent: true, // 默认同意匿名数据收集
```

### 2. 必需性检查
```typescript
// 所有三项都必须同意才能继续
const canProceed = userAgreementAccepted && privacyPolicyAccepted && analyticsConsent;
```

### 3. 退出逻辑
```typescript
// 不同意时显示退出对话框而不是禁用按钮
const handleGetStarted = () => {
  if (canProceed) {
    onGetStarted();
  } else {
    setShowExitDialog(true);
  }
};
```

### 4. 退出应用
```typescript
// 使用 Tauri API 安全退出
await exit(0);
```

## 🔍 用户体验流程

### 1. 正常流程（用户同意所有条款）
1. 用户打开应用
2. 看到欢迎页面，匿名数据收集默认已勾选
3. 用户勾选用户协议和隐私政策
4. 点击"开始使用"按钮
5. 成功进入应用

### 2. 拒绝流程（用户不同意任何条款）
1. 用户打开应用
2. 看到欢迎页面
3. 用户取消勾选任何必需项（包括匿名数据收集）
4. 按钮文本变为"我不同意，退出应用"
5. 点击按钮显示退出确认对话框
6. 用户可以选择"返回重新考虑"或"退出应用"
7. 选择退出应用后，应用安全关闭

## 📊 测试结果

运行 `node test-privacy-policy.js` 的测试结果：

```
✅ 匿名数据收集默认同意
✅ 不同意必要条款时显示退出对话框  
✅ 退出应用功能正常
✅ 缺失同意项检测准确
```

所有测试场景都通过验证。

## 🚀 如何使用

### 1. 启动应用测试
```bash
npm run tauri:dev
```

### 2. 运行功能测试
```bash
node test-privacy-policy.js
```

### 3. 构建生产版本
```bash
npm run tauri:build
```

## ⚠️ 重要说明

### 1. 法律合规
- 确保隐私政策内容符合当地法律法规
- 明确说明数据收集的目的和范围
- 提供用户数据删除和查询的途径

### 2. 用户体验
- 虽然是强制同意，但要确保用户理解数据收集的必要性
- 提供清晰的说明和合理的理由
- 保持界面友好和专业

### 3. 技术实现
- 退出功能在开发环境中可能不会真正关闭窗口
- 生产环境中会正常工作
- 确保数据收集确实是匿名的

## 🔧 自定义配置

如果需要修改默认行为，可以调整以下设置：

### 1. 修改默认同意状态
```typescript
// src/stores/welcomeStore.ts
analyticsConsent: false, // 改为默认不同意
```

### 2. 修改必需性
```typescript
// 如果要让某项变为可选
const canProceed = userAgreementAccepted && privacyPolicyAccepted;
// 移除 && analyticsConsent
```

### 3. 自定义退出对话框内容
编辑 `src/components/Common/ExitConfirmDialog.tsx` 中的文本内容。

## 📞 技术支持

如果在使用过程中遇到问题，请检查：

1. **控制台错误**: 查看浏览器开发者工具的控制台
2. **Tauri 日志**: 查看 Tauri 应用的日志输出
3. **测试脚本**: 运行测试脚本验证逻辑是否正确

## 📝 更新日志

- **2025-08-02**: 实现匿名数据收集默认同意功能
- **2025-08-02**: 添加退出确认对话框
- **2025-08-02**: 完善测试脚本和文档

---

**注意**: 此功能确保了软件的数据收集合规性，同时保持了用户体验的流畅性。请确保在实际部署前充分测试所有场景。
