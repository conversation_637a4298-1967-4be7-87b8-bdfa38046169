/**
 * 启动流程管理器组件
 * 协调整个应用启动流程的核心组件
 */

import React, { useEffect, useState } from 'react';
import { makeStyles } from '@fluentui/react-components';
import { useStartupFlowStore, StartupPhase, UserType } from '../../stores/startupFlowStore';
import { OptimizedUserBehaviorService } from '../../services/optimizedUserBehaviorService';
import { SecurityConfigManager } from '../../config/securityConfig';
import { activationService } from '../../services/activationService';

// 导入各个阶段的组件
import VersionChecker from './VersionChecker';
import UnifiedLoadingVersionChecker from './UnifiedLoadingVersionChecker';
import ForceUpdateModal from './ForceUpdateModal';
import WelcomePage from '../Welcome/WelcomePage';
import InitialSetupWizard from './InitialSetupWizard';
import ActivationCodeValidator from './ActivationCodeValidator';
import ActivationExpiredHandler from './ActivationExpiredHandler';

const useStyles = makeStyles({
  container: {
    width: '100%',
    height: '100vh',
    overflow: 'hidden',
  },
});

interface StartupFlowManagerProps {
  onComplete: () => void;
  onError: (error: string) => void;
}

const StartupFlowManager: React.FC<StartupFlowManagerProps> = ({ onComplete, onError }) => {
  const styles = useStyles();
  const [isInitialized, setIsInitialized] = useState(false);
  
  const {
    currentPhase,
    userType,
    versionCheckResult,
    activationStatus,
    userSettings,
    error,
    retryCount,
    setCurrentPhase,
    setUserType,
    setError,
    resetRetryCount,
    incrementRetryCount,
    setActivationStatus,
    updateUserSettings,
    setActivationVerified,
  } = useStartupFlowStore();

  // 初始化启动流程
  useEffect(() => {
    initializeStartupFlow();
  }, []);

  // 监听阶段变化
  useEffect(() => {
    if (isInitialized) {
      handlePhaseChange();
    }
  }, [currentPhase, isInitialized]);

  const initializeStartupFlow = async () => {
    try {
      // 重置错误状态
      setError(null);
      resetRetryCount();

      // 阶段-1：初始化安全配置
      console.log('🔐 初始化安全配置...');
      const securityConfig = SecurityConfigManager.getInstance();
      await securityConfig.initialize();
      console.log('✅ 安全配置初始化完成');

      // 阶段-0.5：检查本地激活状态
      console.log('🔍 检查本地激活状态...');
      await checkLocalActivationStatus();
      console.log('✅ 激活状态检查完成');

      // 阶段0：用户行为统计上传
      setCurrentPhase('user-behavior-upload');
      await uploadUserBehaviorStats();

      // 进入版本检查阶段
      setCurrentPhase('version-check');
      setIsInitialized(true);

    } catch (error) {
      console.error('启动流程初始化失败:', error);
      const errorMessage = error instanceof Error ? error.message : '初始化失败';
      setError(errorMessage);
      onError(errorMessage);
    }
  };

  const checkLocalActivationStatus = async (): Promise<void> => {
    try {
      // 从激活服务检查本地激活状态
      const activationInfo = activationService.checkActivationStatus();

      console.log('本地激活状态检查结果:', {
        isActivated: activationInfo.isActivated,
        isExpired: activationInfo.isExpired,
        needsActivation: activationInfo.needsActivation,
        expiryDate: activationInfo.expiryDate?.toISOString(),
      });

      if (activationInfo.isActivated && !activationInfo.isExpired) {
        // 激活有效，更新状态
        const remainingDays = activationInfo.expiryDate ?
          Math.ceil((activationInfo.expiryDate.getTime() - Date.now()) / (1000 * 60 * 60 * 24)) :
          undefined;

        setActivationStatus({
          isValid: true,
          isActivated: true,
          expiresAt: activationInfo.expiryDate?.toISOString(),
          activatedAt: new Date().toISOString(),
          remainingDays,
        });

        // 标记为非首次启动
        updateUserSettings({ isFirstLaunch: false });
        setActivationVerified(true);

        console.log('✅ 检测到有效激活状态，已更新状态管理器');
      } else if (activationInfo.isExpired) {
        // 激活已过期
        setActivationStatus({
          isValid: false,
          isActivated: false,
          expiresAt: activationInfo.expiryDate?.toISOString(),
        });
        updateUserSettings({ isFirstLaunch: false });
        console.log('⚠️ 检测到过期激活状态');
      } else {
        // 未激活或无效
        console.log('ℹ️ 未检测到有效激活状态');
      }
    } catch (error) {
      console.warn('检查本地激活状态失败:', error);
      // 检查失败不应阻止启动流程
    }
  };

  const uploadUserBehaviorStats = async (): Promise<void> => {
    try {
      const behaviorService = OptimizedUserBehaviorService.getInstance();

      // 确保服务已初始化
      await behaviorService.initialize();

      await behaviorService.recordAppLaunch();
      console.log('✅ 用户行为统计上传成功');
    } catch (error) {
      console.warn('⚠️ 用户行为统计上传失败，但不影响应用启动:', error);
      // 用户行为统计失败不应阻止启动流程，静默处理
    }
  };

  const handlePhaseChange = () => {
    switch (currentPhase) {
      case 'version-check':
        // 版本检查阶段由VersionChecker组件处理
        break;
      case 'user-type-detection':
        handleUserTypeDetection();
        break;
      case 'activation-verification':
        // 激活验证阶段由相应组件处理
        break;
      case 'completed':
        handleStartupComplete();
        break;
    }
  };

  const handleUserTypeDetection = () => {
    console.log('🔍 开始用户类型检测...', {
      isFirstLaunch: userSettings.isFirstLaunch,
      activationStatus: activationStatus ? {
        isValid: activationStatus.isValid,
        isActivated: activationStatus.isActivated,
        remainingDays: activationStatus.remainingDays,
      } : null,
    });

    // 重新检查激活状态以确保最新数据
    const currentActivationInfo = activationService.checkActivationStatus();
    console.log('🔍 当前激活状态检查结果:', {
      isActivated: currentActivationInfo.isActivated,
      isExpired: currentActivationInfo.isExpired,
      needsActivation: currentActivationInfo.needsActivation,
    });

    // 优先检查激活状态，如果没有激活码或激活码无效，直接跳转到激活页面
    if (currentActivationInfo.needsActivation || !currentActivationInfo.isActivated) {
      // 没有激活码或激活码无效，直接设置为新用户并跳转到激活页面
      setUserType('new');
      updateUserSettings({ isFirstLaunch: true });
      console.log('🔄 未找到有效激活码，自动跳转到激活页面');
      setCurrentPhase('activation-verification');
      return;
    }

    // 有激活码但已过期
    if (currentActivationInfo.isExpired) {
      setUserType('expired');
      console.log('👤 用户类型: 过期用户');
      setCurrentPhase('activation-verification');
      return;
    }

    // 有效激活状态
    if (currentActivationInfo.isActivated && !currentActivationInfo.isExpired) {
      setUserType('existing');
      updateUserSettings({ isFirstLaunch: false });
      console.log('👤 用户类型: 现有用户（有效激活）');
      // 直接完成启动流程
      setCurrentPhase('completed');
      return;
    }

    // 其他情况，默认跳转到激活页面
    setUserType('new');
    updateUserSettings({ isFirstLaunch: true });
    console.log('👤 用户类型: 未知状态，跳转到激活页面');
    setCurrentPhase('activation-verification');
  };

  const handleVersionCheckComplete = (result: any) => {
    // 不再使用弹窗，所有信息都在版本检查页面中显示
    // 无论是否需要更新，都继续到用户类型检测阶段
    console.log('版本检查完成，结果:', result);

    // 延迟一段时间让用户看到版本检查结果
    setTimeout(() => {
      setCurrentPhase('user-type-detection');
    }, result.isLatest ? 1500 : 3000); // 如果有更新，显示更长时间
  };

  const handleVersionCheckError = (error: string) => {
    console.error('版本检查失败:', error);
    
    if (retryCount < 3) {
      incrementRetryCount();
      // 重试版本检查
      setTimeout(() => {
        setCurrentPhase('version-check');
      }, 2000);
    } else {
      // 超过重试次数，继续流程但记录错误
      setError(error);
      setCurrentPhase('user-type-detection');
    }
  };

  const handleForceUpdateComplete = () => {
    // 强制更新完成后，应用会重启
    // 这里可以显示重启提示
    onComplete();
  };

  const handleWelcomeComplete = () => {
    // 新用户欢迎完成，进入初始设置
    // 这里可以直接进入设置向导或跳过
    setCurrentPhase('completed');
  };

  const handleSetupWizardComplete = (settings: any) => {
    // 设置向导完成
    setCurrentPhase('completed');
  };

  const handleActivationSuccess = (newActivationStatus: any) => {
    // 激活成功，完成启动流程
    setCurrentPhase('completed');
  };

  const handleActivationError = (error: string) => {
    console.error('激活验证失败:', error);
    setError(error);
    // 激活失败时，根据用户类型决定下一步
    if (userType === 'new') {
      // 新用户激活失败，可以继续使用试用版
      setCurrentPhase('completed');
    }
  };

  const handleActivationExpiredReactivate = (newStatus: any) => {
    // 重新激活成功
    setCurrentPhase('completed');
  };

  const handleContinueWithLimitations = () => {
    // 继续使用受限功能
    setCurrentPhase('completed');
  };

  const handleStartupComplete = () => {
    // 启动流程完成
    onComplete();
  };

  const renderCurrentPhase = () => {
    switch (currentPhase) {
      case 'user-behavior-upload':
        // 这个阶段在后台进行，不需要UI
        return null;

      case 'version-check':
        return (
          <UnifiedLoadingVersionChecker
            onComplete={handleVersionCheckComplete}
            onError={handleVersionCheckError}
          />
        );

      case 'user-type-detection':
        // 这个阶段在后台进行，不需要UI
        return null;

      case 'activation-verification':
        return renderActivationPhase();

      case 'completed':
        // 启动完成，不需要UI
        return null;

      default:
        return null;
    }
  };

  const renderActivationPhase = () => {
    switch (userType) {
      case 'new':
        // 新用户或无激活码用户，直接显示欢迎页面（会自动跳转到激活页面）
        return (
          <WelcomePage onComplete={handleWelcomeComplete} />
        );

      case 'existing':
        // 现有用户直接完成启动
        setTimeout(() => setCurrentPhase('completed'), 100);
        return null;

      case 'expired':
        return (
          <ActivationExpiredHandler
            activationStatus={activationStatus!}
            onReactivate={handleActivationExpiredReactivate}
            onContinueWithLimitations={handleContinueWithLimitations}
            onPurchase={() => {
              // 打开购买页面
              window.open('https://example.com/purchase', '_blank');
            }}
          />
        );

      case 'unknown':
      default:
        return (
          <ActivationCodeValidator
            onSuccess={handleActivationSuccess}
            onError={handleActivationError}
            onCancel={() => setCurrentPhase('completed')}
          />
        );
    }
  };

  const renderForceUpdateModal = () => {
    // 不再显示强制更新弹窗，所有信息都在版本检查页面中显示
    return null;
  };

  return (
    <div className={styles.container}>
      {renderCurrentPhase()}
      {renderForceUpdateModal()}
    </div>
  );
};

export default StartupFlowManager;
