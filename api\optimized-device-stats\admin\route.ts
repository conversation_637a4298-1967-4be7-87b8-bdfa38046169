/**
 * 管理员API路由
 * 提供数据库性能监控和数据清理功能
 */

import { NextRequest, NextResponse } from 'next/server';
import { OptimizedDeviceStatsService } from '../../../services/optimized-device-stats-service';

// 初始化服务
const deviceStatsService = new OptimizedDeviceStatsService(process.env.DATABASE_URL!);

/**
 * GET /api/optimized-device-stats/admin
 * 获取数据库性能信息
 */
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const action = searchParams.get('action');

    switch (action) {
      case 'performance':
        const performanceInfo = await deviceStatsService.getDatabasePerformanceInfo();
        return NextResponse.json({
          success: true,
          data: performanceInfo,
        });

      case 'cleanup-preview':
        const inactiveDays = searchParams.get('inactiveDays') ? parseInt(searchParams.get('inactiveDays')!) : 365;
        const previewCount = await deviceStatsService.cleanupInactiveDevices(inactiveDays, true);
        return NextResponse.json({
          success: true,
          data: {
            inactiveDays,
            devicesToDelete: previewCount,
            message: `将删除 ${previewCount} 个长期不活跃的设备记录`,
          },
        });

      default:
        return NextResponse.json(
          { success: false, error: '无效的操作类型' },
          { status: 400 }
        );
    }

  } catch (error) {
    console.error('管理员操作失败:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: '服务器内部错误',
        details: error.message 
      },
      { status: 500 }
    );
  }
}

/**
 * DELETE /api/optimized-device-stats/admin
 * 执行数据清理
 */
export async function DELETE(request: NextRequest) {
  try {
    const body = await request.json();
    const { inactiveDays = 365, confirmDelete = false } = body;

    if (!confirmDelete) {
      return NextResponse.json(
        { success: false, error: '请确认删除操作' },
        { status: 400 }
      );
    }

    const deletedCount = await deviceStatsService.cleanupInactiveDevices(inactiveDays, false);

    return NextResponse.json({
      success: true,
      data: {
        deletedCount,
        message: `成功删除 ${deletedCount} 个不活跃设备记录`,
      },
    });

  } catch (error) {
    console.error('数据清理失败:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: '服务器内部错误',
        details: error.message 
      },
      { status: 500 }
    );
  }
}
