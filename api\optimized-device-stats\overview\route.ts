/**
 * 设备统计概览API路由
 * 提供统计概览、热门国家、活动趋势等数据
 */

import { NextRequest, NextResponse } from 'next/server';
import { OptimizedDeviceStatsService } from '../../../services/optimized-device-stats-service';

// 初始化服务
const deviceStatsService = new OptimizedDeviceStatsService(process.env.DATABASE_URL!);

/**
 * GET /api/optimized-device-stats/overview
 * 获取统计概览
 */
export async function GET(request: NextRequest) {
  try {
    const overview = await deviceStatsService.getStatsOverview();

    return NextResponse.json({
      success: true,
      data: overview,
    });

  } catch (error) {
    console.error('获取统计概览失败:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: '服务器内部错误',
        details: error.message 
      },
      { status: 500 }
    );
  }
}
