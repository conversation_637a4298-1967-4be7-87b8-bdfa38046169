/**
 * 激活码验证组件
 * 提供激活码输入界面和实时验证功能
 */

import React, { useState, useEffect } from 'react';
import {
  makeStyles,
  Button,
  Input,
  Text,
  Title1,
  Title2,
  Body1,
  Caption1,
  Card,
  Spinner,
  MessageBar,
  Field,
  ProgressBar,
} from '@fluentui/react-components';
import {
  Key24Regular,
  Checkmark24Filled,
  Dismiss24Filled,
  Warning24Filled,
  Shield24Regular,
} from '@fluentui/react-icons';
import { useStartupFlowStore, ActivationStatus } from '../../stores/startupFlowStore';
import { SecurityConfigManager } from '../../config/securityConfig';
import { SecureDataTransmissionService } from '../../services/secureDataTransmissionService';

const useStyles = makeStyles({
  container: {
    display: 'flex',
    flexDirection: 'column',
    minHeight: '100vh',
    background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
    color: 'white',
    alignItems: 'center',
    justifyContent: 'center',
    padding: '32px',
  },
  card: {
    backgroundColor: 'rgba(255, 255, 255, 0.95)',
    backdropFilter: 'blur(10px)',
    borderRadius: '16px',
    padding: '40px',
    maxWidth: '500px',
    width: '100%',
    color: '#323130',
    boxShadow: '0 8px 32px rgba(0, 0, 0, 0.1)',
  },
  header: {
    textAlign: 'center',
    marginBottom: '32px',
  },
  icon: {
    fontSize: '48px',
    marginBottom: '16px',
    color: '#6264a7',
  },
  inputSection: {
    marginBottom: '24px',
  },
  codeInput: {
    fontSize: '18px',
    textAlign: 'center',
    letterSpacing: '2px',
    fontFamily: 'monospace',
    textTransform: 'uppercase',
  },
  formatHint: {
    textAlign: 'center',
    marginTop: '8px',
    opacity: 0.7,
  },
  validationSection: {
    marginBottom: '24px',
  },
  validationItem: {
    display: 'flex',
    alignItems: 'center',
    gap: '8px',
    marginBottom: '8px',
  },
  validationIcon: {
    fontSize: '16px',
  },
  validIcon: {
    color: '#107c10',
  },
  invalidIcon: {
    color: '#d83b01',
  },
  pendingIcon: {
    color: '#605e5c',
  },
  progressSection: {
    marginBottom: '24px',
  },
  progressText: {
    textAlign: 'center',
    marginBottom: '8px',
  },
  resultSection: {
    marginBottom: '24px',
    padding: '16px',
    borderRadius: '8px',
  },
  successResult: {
    backgroundColor: '#dff6dd',
    border: '1px solid #107c10',
  },
  errorResult: {
    backgroundColor: '#fed9cc',
    border: '1px solid #d83b01',
  },
  actionSection: {
    display: 'flex',
    gap: '12px',
    justifyContent: 'center',
  },
  retryButton: {
    minWidth: '120px',
  },
  activateButton: {
    minWidth: '120px',
  },
});

interface ActivationCodeValidatorProps {
  onSuccess: (activationStatus: ActivationStatus) => void;
  onError: (error: string) => void;
  onCancel?: () => void;
}

const ActivationCodeValidator: React.FC<ActivationCodeValidatorProps> = ({
  onSuccess,
  onError,
  onCancel,
}) => {
  const styles = useStyles();
  const [code, setCode] = useState('');
  const [isValidating, setIsValidating] = useState(false);
  const [validationProgress, setValidationProgress] = useState(0);
  const [validationStatus, setValidationStatus] = useState<'idle' | 'validating' | 'success' | 'error'>('idle');
  const [validationResult, setValidationResult] = useState<ActivationStatus | null>(null);
  const [errorMessage, setErrorMessage] = useState('');

  const { setActivationStatus, setActivationVerified } = useStartupFlowStore();

  // 激活码格式验证规则
  const codeValidations = [
    {
      label: '长度为19个字符',
      validate: (code: string) => code.replace(/-/g, '').length === 16,
    },
    {
      label: '包含3个连字符',
      validate: (code: string) => (code.match(/-/g) || []).length === 3,
    },
    {
      label: '只包含字母和数字',
      validate: (code: string) => /^[A-Z0-9-]+$/.test(code),
    },
    {
      label: '格式为XXXX-XXXX-XXXX-XXXX',
      validate: (code: string) => /^[A-Z0-9]{4}-[A-Z0-9]{4}-[A-Z0-9]{4}-[A-Z0-9]{4}$/.test(code),
    },
  ];

  // 格式化激活码输入
  const formatActivationCode = (input: string): string => {
    // 移除所有非字母数字字符并转换为大写
    const cleaned = input.replace(/[^A-Z0-9]/gi, '').toUpperCase();
    
    // 限制长度为16个字符
    const limited = cleaned.slice(0, 16);
    
    // 添加连字符
    const formatted = limited.replace(/(.{4})/g, '$1-').replace(/-$/, '');
    
    return formatted;
  };

  const handleCodeChange = (value: string) => {
    const formatted = formatActivationCode(value);
    setCode(formatted);
    setValidationStatus('idle');
    setValidationResult(null);
    setErrorMessage('');
  };

  const validateActivationCode = async () => {
    if (!isCodeFormatValid()) {
      setErrorMessage('请输入正确格式的激活码');
      return;
    }

    setIsValidating(true);
    setValidationStatus('validating');
    setValidationProgress(0);
    setErrorMessage('');

    try {
      // 步骤1：初始化安全配置
      setValidationProgress(20);
      const configManager = SecurityConfigManager.getInstance();
      await configManager.initialize();

      // 步骤2：准备验证请求
      setValidationProgress(40);
      const requestData = {
        code: code.replace(/-/g, ''), // 移除连字符
      };

      // 步骤3：发送验证请求
      setValidationProgress(60);
      const result = await sendValidationRequest(requestData);

      // 步骤4：处理验证结果
      setValidationProgress(80);
      await new Promise(resolve => setTimeout(resolve, 500));

      setValidationProgress(100);
      setValidationStatus('success');
      setValidationResult(result);
      setActivationStatus(result);
      setActivationVerified(true);

      // 延迟调用成功回调
      setTimeout(() => {
        onSuccess(result);
      }, 1000);

    } catch (error) {
      console.error('激活码验证失败:', error);
      const errorMsg = error instanceof Error ? error.message : '验证失败';
      setValidationStatus('error');
      setErrorMessage(errorMsg);
      onError(errorMsg);
    } finally {
      setIsValidating(false);
    }
  };

  const sendValidationRequest = async (requestData: any): Promise<ActivationStatus> => {
    const configManager = SecurityConfigManager.getInstance();
    const config = configManager.getConfig();

    // 使用安全数据传输服务
    const secureTransmission = SecureDataTransmissionService.getInstance();
    await secureTransmission.initialize();
    
    const securePacket = secureTransmission.createSecurePacket(requestData, true);

    const response = await fetch(`${config.api_base_url}/api/activation/verify`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-API-Key': config.api_key,
        'X-App-ID': config.app_id,
        'X-Data-Encrypted': 'true',
        'X-Data-Version': securePacket.version,
      },
      body: JSON.stringify(securePacket),
    });

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }

    const result = await response.json();
    
    if (!result.success) {
      throw new Error(result.message || '激活码验证失败');
    }

    const activationData = result.data;

    // 处理新的API响应格式
    return {
      isValid: true, // 如果API调用成功，说明激活码有效
      isActivated: activationData.isUsed || false,
      code: activationData.code || code,
      id: activationData.id,
      createdAt: activationData.createdAt,
      expiresAt: activationData.expiresAt,
      isUsed: activationData.isUsed,
      usedAt: activationData.usedAt,
      isExpired: activationData.isExpired,
      productInfo: activationData.productInfo,
      metadata: activationData.metadata,
      apiValidation: activationData.apiValidation,
      // 计算剩余天数（如果有过期时间）
      remainingDays: activationData.expiresAt ?
        Math.max(0, Math.floor((new Date(activationData.expiresAt).getTime() - Date.now()) / (1000 * 60 * 60 * 24))) :
        undefined,
      gracePeriodDays: 7, // 默认7天宽限期
    };
  };

  const isCodeFormatValid = (): boolean => {
    return codeValidations.every(validation => validation.validate(code));
  };

  const renderValidationChecks = () => {
    return (
      <div className={styles.validationSection}>
        <Caption1 weight="semibold">格式检查:</Caption1>
        {codeValidations.map((validation, index) => {
          const isValid = validation.validate(code);
          const isEmpty = code.length === 0;
          
          return (
            <div key={index} className={styles.validationItem}>
              <div className={styles.validationIcon}>
                {isEmpty ? (
                  <div className={styles.pendingIcon}>○</div>
                ) : isValid ? (
                  <Checkmark24Filled className={styles.validIcon} />
                ) : (
                  <Dismiss24Filled className={styles.invalidIcon} />
                )}
              </div>
              <Caption1>{validation.label}</Caption1>
            </div>
          );
        })}
      </div>
    );
  };

  const renderValidationProgress = () => {
    if (!isValidating) return null;

    return (
      <div className={styles.progressSection}>
        <Text className={styles.progressText}>正在验证激活码...</Text>
        <ProgressBar value={validationProgress / 100} />
      </div>
    );
  };

  const renderValidationResult = () => {
    if (validationStatus === 'success' && validationResult) {
      // 优先使用apiValidation.expiresAt，然后是expiresAt
      const expiryDateStr = validationResult.apiValidation?.expiresAt || validationResult.expiresAt;
      const expiryDate = expiryDateStr ? new Date(expiryDateStr) : null;

      return (
        <div className={`${styles.resultSection} ${styles.successResult}`}>
          <div style={{ display: 'flex', alignItems: 'center', gap: '8px', marginBottom: '8px' }}>
            <Checkmark24Filled style={{ color: '#107c10' }} />
            <Text weight="semibold">
              {validationResult.isUsed ? '激活码已使用' : '激活码验证通过'}
            </Text>
          </div>

          {expiryDate && (
            <Body1>
              有效期至 {expiryDate.toLocaleDateString('zh-CN', {
                year: 'numeric',
                month: 'long',
                day: 'numeric',
                hour: '2-digit',
                minute: '2-digit'
              })}
            </Body1>
          )}

          {validationResult.remainingDays !== undefined && validationResult.remainingDays >= 0 && (
            <Caption1>剩余 {validationResult.remainingDays} 天</Caption1>
          )}

          {validationResult.isExpired && (
            <Caption1 style={{ color: '#d83b01' }}>⚠️ 激活码已过期</Caption1>
          )}

          {validationResult.productInfo && (
            <Caption1>
              产品: {validationResult.productInfo.name} v{validationResult.productInfo.version}
            </Caption1>
          )}
        </div>
      );
    }

    if (validationStatus === 'error' && errorMessage) {
      return (
        <div className={`${styles.resultSection} ${styles.errorResult}`}>
          <div style={{ display: 'flex', alignItems: 'center', gap: '8px', marginBottom: '8px' }}>
            <Warning24Filled style={{ color: '#d83b01' }} />
            <Text weight="semibold">验证失败</Text>
          </div>
          <Body1>{errorMessage}</Body1>
        </div>
      );
    }

    return null;
  };

  return (
    <div className={styles.container}>
      <Card className={styles.card}>
        <div className={styles.header}>
          <div className={styles.icon}>
            <Key24Regular />
          </div>
          <Title1>激活码验证</Title1>
          <Body1>请输入您的激活码以继续使用</Body1>
        </div>

        <div className={styles.inputSection}>
          <Field label="激活码" required>
            <Input
              value={code}
              onChange={(_, data) => handleCodeChange(data.value)}
              placeholder="XXXX-XXXX-XXXX-XXXX"
              className={styles.codeInput}
              disabled={isValidating}
              maxLength={19}
              autoComplete="off"
              data-form-type="other"
              data-lpignore="true"
              spellCheck={false}
              autoCorrect="off"
              autoCapitalize="off"
            />
          </Field>
          <Caption1 className={styles.formatHint}>
            格式：4组4位字符，用连字符分隔
          </Caption1>
        </div>

        {renderValidationChecks()}
        {renderValidationProgress()}
        {renderValidationResult()}

        <div className={styles.actionSection}>
          {onCancel && (
            <Button
              appearance="secondary"
              onClick={onCancel}
              disabled={isValidating}
            >
              取消
            </Button>
          )}
          <Button
            appearance="primary"
            className={styles.activateButton}
            icon={<Shield24Regular />}
            disabled={!isCodeFormatValid() || isValidating}
            onClick={validateActivationCode}
          >
            {isValidating ? <Spinner size="tiny" /> : '验证激活码'}
          </Button>
        </div>
      </Card>
    </div>
  );
};

export default ActivationCodeValidator;
