-- 玩机管家用户行为统计数据库优化迁移
-- 创建时间: 2025-08-01
-- 目标: 在0.38GB存储限制内优化数据结构

-- 1. 创建优化后的设备统计表
CREATE TABLE IF NOT EXISTS device_stats (
    id SERIAL PRIMARY KEY,
    device_fingerprint VARCHAR(64) UNIQUE NOT NULL,
    install_rank INTEGER NOT NULL,
    country_code CHAR(2),
    region_code VARCHAR(10),
    os_version VARCHAR(20),
    arch VARCHAR(10),
    run_count INTEGER DEFAULT 1,
    first_seen TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_seen TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 2. 创建地理位置代码表
CREATE TABLE IF NOT EXISTS geo_codes (
    id SERIAL PRIMARY KEY,
    country_code CHAR(2) NOT NULL,
    country_name VARCHAR(50) NOT NULL,
    region_code VARCHAR(10),
    region_name VARCHAR(50),
    UNIQUE(country_code, region_code)
);

-- 3. 创建索引优化查询性能
CREATE INDEX IF NOT EXISTS idx_device_fingerprint ON device_stats(device_fingerprint);
CREATE INDEX IF NOT EXISTS idx_install_rank ON device_stats(install_rank);
CREATE INDEX IF NOT EXISTS idx_country_region ON device_stats(country_code, region_code);
CREATE INDEX IF NOT EXISTS idx_last_seen ON device_stats(last_seen);
CREATE INDEX IF NOT EXISTS idx_run_count ON device_stats(run_count);

-- 4. 预填充地理位置数据
INSERT INTO geo_codes (country_code, country_name, region_code, region_name) VALUES
-- 中国主要省份
('CN', '中国', 'BJ', '北京'),
('CN', '中国', 'SH', '上海'),
('CN', '中国', 'TJ', '天津'),
('CN', '中国', 'CQ', '重庆'),
('CN', '中国', 'GD', '广东'),
('CN', '中国', 'JS', '江苏'),
('CN', '中国', 'ZJ', '浙江'),
('CN', '中国', 'SD', '山东'),
('CN', '中国', 'HN', '河南'),
('CN', '中国', 'HB', '河北'),
('CN', '中国', 'SX', '山西'),
('CN', '中国', 'LN', '辽宁'),
('CN', '中国', 'JL', '吉林'),
('CN', '中国', 'HL', '黑龙江'),
('CN', '中国', 'AH', '安徽'),
('CN', '中国', 'FJ', '福建'),
('CN', '中国', 'JX', '江西'),
('CN', '中国', 'HuN', '湖南'),
('CN', '中国', 'HuB', '湖北'),
('CN', '中国', 'SC', '四川'),
('CN', '中国', 'YN', '云南'),
('CN', '中国', 'GZ', '贵州'),
('CN', '中国', 'SN', '陕西'),
('CN', '中国', 'GS', '甘肃'),
('CN', '中国', 'QH', '青海'),
('CN', '中国', 'NX', '宁夏'),
('CN', '中国', 'XJ', '新疆'),
('CN', '中国', 'XZ', '西藏'),
('CN', '中国', 'GX', '广西'),
('CN', '中国', 'NM', '内蒙古'),
('CN', '中国', 'HK', '香港'),
('CN', '中国', 'MO', '澳门'),
('CN', '中国', 'TW', '台湾'),

-- 其他国家
('US', '美国', 'CA', '加利福尼亚'),
('US', '美国', 'NY', '纽约'),
('US', '美国', 'TX', '德克萨斯'),
('US', '美国', 'FL', '佛罗里达'),
('JP', '日本', 'TK', '东京'),
('JP', '日本', 'OS', '大阪'),
('KR', '韩国', 'SE', '首尔'),
('KR', '韩国', 'BS', '釜山'),
('GB', '英国', 'EN', '英格兰'),
('GB', '英国', 'SC', '苏格兰'),
('DE', '德国', 'BY', '巴伐利亚'),
('DE', '德国', 'NW', '北莱茵-威斯特法伦'),
('FR', '法国', 'IF', '法兰西岛'),
('FR', '法国', 'PR', '普罗旺斯'),
('CA', '加拿大', 'ON', '安大略'),
('CA', '加拿大', 'QC', '魁北克'),
('AU', '澳大利亚', 'NSW', '新南威尔士'),
('AU', '澳大利亚', 'VIC', '维多利亚'),
('SG', '新加坡', 'SG', '新加坡'),
('MY', '马来西亚', 'KL', '吉隆坡'),
('TH', '泰国', 'BK', '曼谷'),
('VN', '越南', 'HN', '河内'),
('IN', '印度', 'DL', '德里'),
('IN', '印度', 'MH', '马哈拉施特拉')
ON CONFLICT (country_code, region_code) DO NOTHING;

-- 5. 创建自动更新时间戳的触发器
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_device_stats_updated_at 
    BEFORE UPDATE ON device_stats 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column();

-- 6. 创建获取下一个安装排名的函数
CREATE OR REPLACE FUNCTION get_next_install_rank()
RETURNS INTEGER AS $$
DECLARE
    next_rank INTEGER;
BEGIN
    SELECT COALESCE(MAX(install_rank), 0) + 1 INTO next_rank FROM device_stats;
    RETURN next_rank;
END;
$$ LANGUAGE plpgsql;

-- 7. 创建批量更新运行次数的函数
CREATE OR REPLACE FUNCTION batch_update_run_counts(
    fingerprints TEXT[],
    increments INTEGER[]
)
RETURNS INTEGER AS $$
DECLARE
    updated_count INTEGER := 0;
    i INTEGER;
BEGIN
    FOR i IN 1..array_length(fingerprints, 1) LOOP
        UPDATE device_stats 
        SET run_count = run_count + increments[i],
            last_seen = CURRENT_TIMESTAMP,
            updated_at = CURRENT_TIMESTAMP
        WHERE device_fingerprint = fingerprints[i];
        
        GET DIAGNOSTICS updated_count = updated_count + ROW_COUNT;
    END LOOP;
    
    RETURN updated_count;
END;
$$ LANGUAGE plpgsql;

-- 8. 创建地理位置解析函数
CREATE OR REPLACE FUNCTION resolve_geo_codes(
    country_input TEXT,
    region_input TEXT DEFAULT NULL
)
RETURNS TABLE(country_code CHAR(2), region_code VARCHAR(10)) AS $$
BEGIN
    RETURN QUERY
    SELECT gc.country_code, gc.region_code
    FROM geo_codes gc
    WHERE (gc.country_name ILIKE '%' || country_input || '%' 
           OR gc.country_code = UPPER(country_input))
    AND (region_input IS NULL 
         OR gc.region_name ILIKE '%' || region_input || '%'
         OR gc.region_code = UPPER(region_input))
    LIMIT 1;
END;
$$ LANGUAGE plpgsql;

-- 9. 创建统计视图
CREATE OR REPLACE VIEW device_stats_summary AS
SELECT 
    COUNT(*) as total_devices,
    SUM(run_count) as total_runs,
    AVG(run_count) as avg_runs_per_device,
    COUNT(DISTINCT country_code) as countries_count,
    MAX(install_rank) as latest_install_rank,
    COUNT(CASE WHEN last_seen >= CURRENT_DATE - INTERVAL '7 days' THEN 1 END) as active_last_7_days,
    COUNT(CASE WHEN last_seen >= CURRENT_DATE - INTERVAL '30 days' THEN 1 END) as active_last_30_days
FROM device_stats;

-- 10. 创建国家统计视图
CREATE OR REPLACE VIEW country_stats AS
SELECT 
    gc.country_name,
    gc.country_code,
    COUNT(ds.id) as device_count,
    SUM(ds.run_count) as total_runs,
    AVG(ds.run_count) as avg_runs,
    MAX(ds.last_seen) as last_activity
FROM device_stats ds
LEFT JOIN geo_codes gc ON ds.country_code = gc.country_code
GROUP BY gc.country_name, gc.country_code
ORDER BY device_count DESC;

-- 11. 添加数据完整性约束
ALTER TABLE device_stats 
ADD CONSTRAINT check_install_rank_positive 
CHECK (install_rank > 0);

ALTER TABLE device_stats 
ADD CONSTRAINT check_run_count_positive 
CHECK (run_count > 0);

ALTER TABLE device_stats 
ADD CONSTRAINT check_device_fingerprint_length 
CHECK (LENGTH(device_fingerprint) >= 16);

-- 12. 创建数据清理函数（可选，用于定期清理）
CREATE OR REPLACE FUNCTION cleanup_old_inactive_devices(
    inactive_days INTEGER DEFAULT 365
)
RETURNS INTEGER AS $$
DECLARE
    deleted_count INTEGER;
BEGIN
    DELETE FROM device_stats 
    WHERE last_seen < CURRENT_DATE - INTERVAL '1 day' * inactive_days
    AND run_count = 1;  -- 只删除只运行过一次且长期不活跃的设备
    
    GET DIAGNOSTICS deleted_count = ROW_COUNT;
    RETURN deleted_count;
END;
$$ LANGUAGE plpgsql;

-- 13. 创建性能监控视图
CREATE OR REPLACE VIEW database_size_info AS
SELECT 
    schemaname,
    tablename,
    pg_size_pretty(pg_total_relation_size(schemaname||'.'||tablename)) as size,
    pg_total_relation_size(schemaname||'.'||tablename) as size_bytes
FROM pg_tables 
WHERE tablename IN ('device_stats', 'geo_codes')
ORDER BY pg_total_relation_size(schemaname||'.'||tablename) DESC;

-- 完成迁移
COMMENT ON TABLE device_stats IS '优化后的设备统计表 - 支持100万+设备记录';
COMMENT ON TABLE geo_codes IS '地理位置代码表 - 节省存储空间';
COMMENT ON FUNCTION get_next_install_rank() IS '获取下一个安装排名';
COMMENT ON FUNCTION batch_update_run_counts(TEXT[], INTEGER[]) IS '批量更新运行次数';
COMMENT ON VIEW device_stats_summary IS '设备统计概览';
COMMENT ON VIEW country_stats IS '国家统计信息';
