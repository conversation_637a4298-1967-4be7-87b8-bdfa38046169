/**
 * 启动流程状态管理
 * 管理应用启动时的各个阶段和状态
 */

import { create } from 'zustand';
import { persist, createJSONStorage } from 'zustand/middleware';

// 启动流程阶段
export type StartupPhase = 
  | 'user-behavior-upload'    // 阶段0：用户行为统计上传
  | 'version-check'           // 阶段1：版本检查
  | 'user-type-detection'     // 阶段2：用户类型判断
  | 'activation-verification' // 阶段3：激活状态验证
  | 'completed';              // 完成

// 用户类型
export type UserType = 'new' | 'existing' | 'expired' | 'unknown';

// 版本检查结果
export interface VersionCheckResult {
  isLatest: boolean;
  currentVersion: string;
  latestVersion?: string;
  updateInfo?: {
    id?: number;
    version: string;
    releaseNotes?: string;
    releaseNotesEn?: string;
    releaseDate: string;
    downloadLinks?: {
      official?: string;
      quark?: string;
      baidu?: string;
      github?: string;
    };
    fileSize?: string;
    isStable?: boolean;
    versionType?: "release" | "beta" | "alpha";
    metadata?: {
      buildNumber?: string;
      commitHash?: string;
      changelog?: string[];
    };
    // 兼容旧格式
    title?: string;
    description?: string;
    downloadUrl?: string;
    isForced?: boolean;
  };
}

// 激活状态（符合API响应格式）
export interface ActivationStatus {
  isValid: boolean;
  isActivated: boolean;
  code?: string;
  expiresAt?: string;
  activatedAt?: string;
  remainingDays?: number;
  gracePeriodDays?: number;
  metadata?: any;
  // 新增字段，符合API文档
  id?: string;
  createdAt?: string;
  isUsed?: boolean;
  usedAt?: string;
  isExpired?: boolean;
  productInfo?: {
    name: string;
    version: string;
    features: string[];
  };
  apiValidation?: {
    expiresAt: string;
    remainingTime?: number;
    message?: string;
  };
}

// 用户设置
export interface UserSettings {
  theme: 'light' | 'dark' | 'auto';
  language: 'zh-CN' | 'en-US';
  privacyConsent: boolean;
  dataCollectionConsent: boolean;
  analyticsConsent: boolean;
  isFirstLaunch: boolean;
}

// 启动流程状态
export interface StartupFlowState {
  // 当前阶段
  currentPhase: StartupPhase;
  
  // 各阶段状态
  userBehaviorUploaded: boolean;
  versionCheckCompleted: boolean;
  userTypeDetected: boolean;
  activationVerified: boolean;
  
  // 用户信息
  userType: UserType;
  isFirstLaunch: boolean;
  
  // 版本信息
  versionCheckResult: VersionCheckResult | null;
  
  // 激活信息
  activationStatus: ActivationStatus | null;
  
  // 用户设置
  userSettings: UserSettings;
  
  // 错误状态
  error: string | null;
  isLoading: boolean;
  
  // 重试计数
  retryCount: number;
  maxRetries: number;
}

// 启动流程操作
export interface StartupFlowActions {
  // 阶段控制
  setCurrentPhase: (phase: StartupPhase) => void;
  nextPhase: () => void;
  
  // 用户行为统计
  setUserBehaviorUploaded: (uploaded: boolean) => void;
  
  // 版本检查
  setVersionCheckResult: (result: VersionCheckResult) => void;
  setVersionCheckCompleted: (completed: boolean) => void;
  
  // 用户类型
  setUserType: (type: UserType) => void;
  setIsFirstLaunch: (isFirst: boolean) => void;
  setUserTypeDetected: (detected: boolean) => void;
  
  // 激活状态
  setActivationStatus: (status: ActivationStatus) => void;
  setActivationVerified: (verified: boolean) => void;
  
  // 用户设置
  updateUserSettings: (settings: Partial<UserSettings>) => void;
  
  // 错误处理
  setError: (error: string | null) => void;
  setLoading: (loading: boolean) => void;
  
  // 重试机制
  incrementRetryCount: () => void;
  resetRetryCount: () => void;
  
  // 重置状态
  resetStartupFlow: () => void;
  
  // 完成启动流程
  completeStartupFlow: () => void;
}

// 默认用户设置
const defaultUserSettings: UserSettings = {
  theme: 'auto',
  language: 'zh-CN',
  privacyConsent: false,
  dataCollectionConsent: false,
  analyticsConsent: false,
  isFirstLaunch: true,
};

// 初始状态
const initialState: StartupFlowState = {
  currentPhase: 'user-behavior-upload',
  userBehaviorUploaded: false,
  versionCheckCompleted: false,
  userTypeDetected: false,
  activationVerified: false,
  userType: 'unknown',
  isFirstLaunch: false,
  versionCheckResult: null,
  activationStatus: null,
  userSettings: { ...defaultUserSettings },
  error: null,
  isLoading: false,
  retryCount: 0,
  maxRetries: 3,
};

// 阶段顺序映射
const phaseOrder: StartupPhase[] = [
  'user-behavior-upload',
  'version-check',
  'user-type-detection',
  'activation-verification',
  'completed'
];

export const useStartupFlowStore = create<StartupFlowState & StartupFlowActions>()(
  persist(
    (set, get) => ({
      ...initialState,

      // 阶段控制
      setCurrentPhase: (phase: StartupPhase) => set({ currentPhase: phase }),
      
      nextPhase: () => {
        const currentPhase = get().currentPhase;
        const currentIndex = phaseOrder.indexOf(currentPhase);
        if (currentIndex < phaseOrder.length - 1) {
          const nextPhase = phaseOrder[currentIndex + 1];
          set({ currentPhase: nextPhase });
        }
      },

      // 用户行为统计
      setUserBehaviorUploaded: (uploaded: boolean) => 
        set({ userBehaviorUploaded: uploaded }),

      // 版本检查
      setVersionCheckResult: (result: VersionCheckResult) => 
        set({ versionCheckResult: result }),
      
      setVersionCheckCompleted: (completed: boolean) => 
        set({ versionCheckCompleted: completed }),

      // 用户类型
      setUserType: (type: UserType) => set({ userType: type }),
      
      setIsFirstLaunch: (isFirst: boolean) => set({ isFirstLaunch: isFirst }),
      
      setUserTypeDetected: (detected: boolean) => 
        set({ userTypeDetected: detected }),

      // 激活状态
      setActivationStatus: (status: ActivationStatus) => 
        set({ activationStatus: status }),
      
      setActivationVerified: (verified: boolean) => 
        set({ activationVerified: verified }),

      // 用户设置
      updateUserSettings: (settings: Partial<UserSettings>) =>
        set((state) => ({
          userSettings: { ...state.userSettings, ...settings }
        })),

      // 错误处理
      setError: (error: string | null) => set({ error }),
      
      setLoading: (loading: boolean) => set({ isLoading: loading }),

      // 重试机制
      incrementRetryCount: () => 
        set((state) => ({ retryCount: state.retryCount + 1 })),
      
      resetRetryCount: () => set({ retryCount: 0 }),

      // 重置状态
      resetStartupFlow: () => set({ ...initialState }),

      // 完成启动流程
      completeStartupFlow: () => 
        set({ 
          currentPhase: 'completed',
          error: null,
          isLoading: false 
        }),
    }),
    {
      name: 'hout-startup-flow-storage',
      storage: createJSONStorage(() => localStorage),
      partialize: (state) => ({
        userType: state.userType,
        isFirstLaunch: state.isFirstLaunch,
        userSettings: state.userSettings,
        activationStatus: state.activationStatus,
      }),
    }
  )
);

// 辅助函数：检查是否可以进入下一阶段
export const canProceedToNextPhase = (phase: StartupPhase, state: StartupFlowState): boolean => {
  switch (phase) {
    case 'user-behavior-upload':
      return state.userBehaviorUploaded;
    case 'version-check':
      return state.versionCheckCompleted;
    case 'user-type-detection':
      return state.userTypeDetected;
    case 'activation-verification':
      return state.activationVerified;
    case 'completed':
      return true;
    default:
      return false;
  }
};

// 辅助函数：获取阶段显示名称
export const getPhaseDisplayName = (phase: StartupPhase): string => {
  switch (phase) {
    case 'user-behavior-upload':
      return '上传使用统计';
    case 'version-check':
      return '检查版本更新';
    case 'user-type-detection':
      return '检测用户类型';
    case 'activation-verification':
      return '验证激活状态';
    case 'completed':
      return '启动完成';
    default:
      return '未知阶段';
  }
};

// 辅助函数：获取阶段进度百分比
export const getPhaseProgress = (phase: StartupPhase): number => {
  const index = phaseOrder.indexOf(phase);
  return ((index + 1) / phaseOrder.length) * 100;
};
