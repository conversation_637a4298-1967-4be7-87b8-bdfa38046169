/**
 * 国家统计API路由
 * 提供国家分布统计数据
 */

import { NextRequest, NextResponse } from 'next/server';
import { OptimizedDeviceStatsService } from '../../../services/optimized-device-stats-service';

// 初始化服务
const deviceStatsService = new OptimizedDeviceStatsService(process.env.DATABASE_URL!);

/**
 * GET /api/optimized-device-stats/countries
 * 获取热门国家统计
 */
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const limit = searchParams.get('limit') ? parseInt(searchParams.get('limit')!) : 10;

    const countries = await deviceStatsService.getTopCountries(limit);

    return NextResponse.json({
      success: true,
      data: countries,
    });

  } catch (error) {
    console.error('获取国家统计失败:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: '服务器内部错误',
        details: error.message 
      },
      { status: 500 }
    );
  }
}
