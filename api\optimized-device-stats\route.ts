/**
 * 优化后的设备统计API路由
 * 专为存储空间限制优化，支持高效的数据操作
 */

import { NextRequest, NextResponse } from 'next/server';
import { OptimizedDeviceStatsService } from '../../services/optimized-device-stats-service';
import { DeviceStatsRequest, DeviceStatsFilter } from '../../database/schema/optimized-device-stats';

// 初始化服务
const deviceStatsService = new OptimizedDeviceStatsService(process.env.DATABASE_URL!);

/**
 * POST /api/optimized-device-stats
 * 记录或更新设备统计
 */
export async function POST(request: NextRequest) {
  try {
    const body: DeviceStatsRequest = await request.json();

    // 验证必需字段
    if (!body.deviceFingerprint) {
      return NextResponse.json(
        { success: false, error: '设备指纹不能为空' },
        { status: 400 }
      );
    }

    // 获取客户端IP地址
    const ipAddress = request.headers.get('x-forwarded-for') || 
                     request.headers.get('x-real-ip') || 
                     request.ip;

    // 记录设备统计
    const result = await deviceStatsService.recordDeviceStats({
      ...body,
      ipAddress: ipAddress || undefined,
    });

    return NextResponse.json(result);

  } catch (error) {
    console.error('记录设备统计失败:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: '服务器内部错误',
        details: error.message 
      },
      { status: 500 }
    );
  }
}

/**
 * GET /api/optimized-device-stats
 * 查询设备统计（支持过滤和分页）
 */
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);

    // 解析查询参数
    const filter: DeviceStatsFilter = {
      countryCode: searchParams.get('countryCode') || undefined,
      regionCode: searchParams.get('regionCode') || undefined,
      osVersion: searchParams.get('osVersion') || undefined,
      arch: searchParams.get('arch') || undefined,
      minRunCount: searchParams.get('minRunCount') ? parseInt(searchParams.get('minRunCount')!) : undefined,
      maxRunCount: searchParams.get('maxRunCount') ? parseInt(searchParams.get('maxRunCount')!) : undefined,
      dateFrom: searchParams.get('dateFrom') ? new Date(searchParams.get('dateFrom')!) : undefined,
      dateTo: searchParams.get('dateTo') ? new Date(searchParams.get('dateTo')!) : undefined,
      limit: searchParams.get('limit') ? parseInt(searchParams.get('limit')!) : 50,
      offset: searchParams.get('offset') ? parseInt(searchParams.get('offset')!) : 0,
    };

    const result = await deviceStatsService.queryDeviceStats(filter);

    return NextResponse.json({
      success: true,
      data: result,
    });

  } catch (error) {
    console.error('查询设备统计失败:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: '服务器内部错误',
        details: error.message 
      },
      { status: 500 }
    );
  }
}

/**
 * PUT /api/optimized-device-stats
 * 批量更新运行次数
 */
export async function PUT(request: NextRequest) {
  try {
    const body = await request.json();
    const { updates } = body;

    if (!Array.isArray(updates)) {
      return NextResponse.json(
        { success: false, error: '更新数据格式错误' },
        { status: 400 }
      );
    }

    const updatedCount = await deviceStatsService.batchUpdateRunCounts(updates);

    return NextResponse.json({
      success: true,
      updatedCount,
      message: `成功更新 ${updatedCount} 条记录`,
    });

  } catch (error) {
    console.error('批量更新运行次数失败:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: '服务器内部错误',
        details: error.message 
      },
      { status: 500 }
    );
  }
}
